import 'dart:async';
import 'dart:developer';
import 'dart:isolate';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:schnell_pole_installation/survey/survey_service.dart';
import 'package:schnell_pole_installation/survey/s3_upload_service.dart';
import 'package:schnell_pole_installation/survey/models/survey_image_model.dart';
import 'package:schnell_pole_installation/utils/image_compression_util.dart';
import 'package:schnell_pole_installation/utils/utility.dart';

/// Data class for isolate initialization
class _IsolateInitData {
  final SendPort sendPort;
  final RootIsolateToken rootIsolateToken;
  final String hivePath;

  _IsolateInitData({
    required this.sendPort,
    required this.rootIsolateToken,
    required this.hivePath,
  });
}

/// Automatically syncs data whenever internet connection is restored
class ConnectivitySyncService {
  static ConnectivitySyncService? _instance;
  static ConnectivitySyncService get instance =>
      _instance ??= ConnectivitySyncService._();

  ConnectivitySyncService._();

  // Sync state management
  bool _isInitialized = false;
  bool _isSyncing = false;

  // Debounce mechanism for sync operations
  static DateTime? _lastSyncAttempt;
  static const Duration _syncDebounceDelay = Duration(seconds: 3);

  // Connectivity monitoring
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Isolate management for background processing
  Isolate? _syncIsolate;
  SendPort? _syncSendPort;
  ReceivePort? _syncReceivePort;
  bool _isolateInitialized = false;

  // Progress tracking
  final StreamController<SyncProgress> _progressController =
      StreamController<SyncProgress>.broadcast();
  Stream<SyncProgress> get progressStream => _progressController.stream;

  // Sync status tracking
  final StreamController<SyncStatus> _statusController =
      StreamController<SyncStatus>.broadcast();
  Stream<SyncStatus> get statusStream => _statusController.stream;

  /// Initialize connectivity-based sync
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      log('Initializing Connectivity-based background sync...');

      // Initialize isolate for background processing
      await _initializeIsolate();

      // Start listening to connectivity changes
      _startConnectivityMonitoring();

      _isInitialized = true;
      log('Connectivity-based background sync initialized successfully');
    } catch (e) {
      log('Failed to initialize Connectivity sync: $e');
      rethrow;
    }
  }

  /// Initialize isolate for background sync processing
  Future<void> _initializeIsolate() async {
    if (_isolateInitialized) return;

    try {
      log('Initializing sync isolate...');

      // Create receive port for communication from isolate
      _syncReceivePort = ReceivePort();

      // Get the root isolate token for background isolate initialization
      final rootIsolateToken = RootIsolateToken.instance;
      if (rootIsolateToken == null) {
        throw Exception('RootIsolateToken is not available');
      }

      // Get the Hive path to ensure isolate uses the same path as main thread
      final directory = await getApplicationDocumentsDirectory();
      final hivePath = directory.path;

      // Spawn the isolate with initialization data
      _syncIsolate = await Isolate.spawn<_IsolateInitData>(
        _syncIsolateEntryPoint,
        _IsolateInitData(
          sendPort: _syncReceivePort!.sendPort,
          rootIsolateToken: rootIsolateToken,
          hivePath: hivePath,
        ),
      );

      // Wait for isolate to send back its SendPort and handle ongoing messages
      final completer = Completer<SendPort>();
      bool sendPortReceived = false;

      _syncReceivePort!.listen((message) {
        if (!sendPortReceived && message is SendPort) {
          _syncSendPort = message;
          sendPortReceived = true;
          completer.complete(message);
        } else {
          // Handle ongoing messages from isolate
          _handleIsolateMessage(message);
        }
      });

      await completer.future;
      _isolateInitialized = true;
      log('Sync isolate initialized successfully');
    } catch (e) {
      log('Failed to initialize sync isolate: $e');
      rethrow;
    }
  }

  /// Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        _handleConnectivityChange(results);
      },
    );

    log('Started monitoring connectivity changes');

    // Check current connectivity and trigger sync if already online
    _checkCurrentConnectivity();
  }

  /// Check current connectivity and trigger sync if online
  void _checkCurrentConnectivity() {
    Connectivity().checkConnectivity().then((results) {
      _handleConnectivityChange(results);
    }).catchError((e) {
      log('Error checking current connectivity: $e');
    });
  }

  /// Handle connectivity state changes
  void _handleConnectivityChange(List<ConnectivityResult> results) async {
    final bool isConnected = results.any((result) =>
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet);

    log('Connectivity changed: $results, Connected: $isConnected');

    if (isConnected) {
      // Internet is available - trigger sync
      log('Internet connection available - triggering background sync');
      _triggerBackgroundSync();
    }
  }

  /// Trigger immediate background sync
  void _triggerBackgroundSync() {
    log('_triggerBackgroundSync called - scheduling background sync');
    // Don't block the connectivity callback - run sync asynchronously
    Future.microtask(() => _performBackgroundSync());
  }

  /// Perform manual sync (called by UI)
  Future<void> performManualSync() async {
    if (!_isInitialized) {
      await initialize();
    }

    // Manual sync bypasses debounce (user-initiated)
    _lastSyncAttempt = null;
    await _performBackgroundSync();
  }

  /// Trigger sync immediately after data insertion
  void triggerSyncAfterDataInsertion() {
    if (!_isInitialized) {
      log('Sync service not initialized, skipping immediate sync');
      return;
    }

    log('Data inserted - triggering immediate background sync');
    // Reset debounce to allow immediate sync
    _lastSyncAttempt = null;
    // Trigger sync asynchronously
    Future.microtask(() => _performBackgroundSync());
  }

  /// Handle messages from isolate
  void _handleIsolateMessage(dynamic message) {
    if (message is Map<String, dynamic>) {
      final type = message['type'] as String?;

      switch (type) {
        case 'progress':
          final progress = SyncProgress.fromMap(message['data']);
          _progressController.add(progress);
          break;

        case 'status':
          final status = SyncStatus.values[message['data'] as int];
          _statusController.add(status);
          break;

        case 'log':
          log('Isolate: ${message['data']}');
          break;

        case 'sync_complete':
          _isSyncing = false;
          log('Background sync completed in isolate');
          // Refresh main thread Hive boxes to reflect isolate changes
          _refreshMainThreadHiveBoxes().then((_) {
            _statusController.add(SyncStatus.completed);
            log('Sync complete status sent after Hive refresh');
          }).catchError((e) {
            log('Error during Hive refresh: $e');
            _statusController.add(SyncStatus.error);
          });
          break;

        case 'sync_error':
          _isSyncing = false;
          _statusController.add(SyncStatus.error);
          log('Background sync failed in isolate: ${message['data']}');
          break;
      }
    }
  }

  /// Refresh main thread Hive boxes to reflect isolate changes
  Future<void> _refreshMainThreadHiveBoxes() async {
    try {
      log('Refreshing main thread Hive boxes after isolate sync...');

      // Small delay to ensure isolate file operations are fully committed
      await Future.delayed(const Duration(milliseconds: 100));

      // Close and reopen survey box to get latest data from disk
      if (Hive.isBoxOpen('surveyBox')) {
        await Hive.box('surveyBox').close();
      }
      final surveyBox = await Hive.openBox('surveyBox');

      // Close and reopen images box to get latest data from disk
      if (Hive.isBoxOpen('imagesBox')) {
        await Hive.box('imagesBox').close();
      }
      final imagesBox = await Hive.openBox('imagesBox');

      // Verify the refresh worked by checking upload status
      int uploadedSurveys = 0;
      int uploadedImages = 0;

      for (var entry in surveyBox.values) {
        if (entry is Map && entry['isUploaded'] == true) {
          uploadedSurveys++;
        }
      }

      for (int i = 0; i < imagesBox.length; i++) {
        final imageMap = imagesBox.getAt(i) as Map<dynamic, dynamic>?;
        if (imageMap != null) {
          final model = MultiCapturedImageModel.fromMap(imageMap);
          if (model.isAllUploaded) {
            uploadedImages++;
          }
        }
      }

      log('Main thread Hive boxes refreshed successfully');
      log('Verification: $uploadedSurveys surveys uploaded, $uploadedImages image records fully uploaded');
    } catch (e) {
      log('Error refreshing main thread Hive boxes: $e');
      rethrow;
    }
  }

  /// Isolate entry point for background sync processing
  static _syncIsolateEntryPoint(_IsolateInitData initData) async {
    try {
      // Initialize the background isolate binary messenger
      BackgroundIsolateBinaryMessenger.ensureInitialized(
          initData.rootIsolateToken);

      // Initialize Hive in the isolate
      await _initializeHiveInIsolate(initData.sendPort, initData.hivePath);

      // Create receive port for this isolate
      final isolateReceivePort = ReceivePort();

      // Send the isolate's send port back to main isolate
      initData.sendPort.send(isolateReceivePort.sendPort);

      // Listen for sync requests from main isolate
      await for (final message in isolateReceivePort) {
        if (message is Map<String, dynamic>) {
          final type = message['type'] as String?;

          switch (type) {
            case 'sync_request':
              await _performIsolateSyncOperation(initData.sendPort);
              break;

            case 'shutdown':
              isolateReceivePort.close();
              return;
          }
        }
      }
    } catch (e) {
      initData.sendPort
          .send({'type': 'log', 'data': 'Error in isolate entry point: $e'});
    }
  }

  /// Initialize Hive in the isolate
  static Future<void> _initializeHiveInIsolate(
      SendPort mainSendPort, String hivePath) async {
    try {
      mainSendPort
          .send({'type': 'log', 'data': 'Initializing Hive in isolate...'});

      // Initialize Hive in the isolate using the exact same path as main thread
      Hive.init(hivePath);

      mainSendPort.send({
        'type': 'log',
        'data': 'Hive initialized successfully in isolate with path: $hivePath'
      });
    } catch (e) {
      mainSendPort.send(
          {'type': 'log', 'data': 'Failed to initialize Hive in isolate: $e'});
      rethrow;
    }
  }

  /// Internal method to perform sync (now delegates to isolate)
  Future<void> _performBackgroundSync() async {
    // Debounce: prevent rapid successive calls
    final now = DateTime.now();
    if (_lastSyncAttempt != null &&
        now.difference(_lastSyncAttempt!) < _syncDebounceDelay) {
      log('Background sync attempt debounced - too soon since last attempt');
      return;
    }
    _lastSyncAttempt = now;

    if (_isSyncing) {
      log('Sync already in progress, skipping...');
      return;
    }

    if (!_isolateInitialized || _syncSendPort == null) {
      log('Isolate not initialized, skipping sync. _isolateInitialized: $_isolateInitialized, _syncSendPort: $_syncSendPort');
      _statusController.add(SyncStatus.error);
      return;
    }

    try {
      _isSyncing = true;
      _statusController.add(SyncStatus.syncing);
      log('Delegating sync to background isolate...');

      // Send sync request to isolate
      _syncSendPort!.send({'type': 'sync_request', 'data': {}});
    } catch (e) {
      log('Failed to delegate sync to isolate: $e');
      _isSyncing = false;
      _statusController.add(SyncStatus.error);
    }
  }

  /// Get location from coordinates
  Future<String> getLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    var locationData =
        '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
    return locationData;
  }

  /// Stop connectivity monitoring
  void stopMonitoring() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
    log('Stopped connectivity monitoring');
  }

  /// Dispose resources
  void dispose() {
    stopMonitoring();
    _disposeIsolate();
    _progressController.close();
    _statusController.close();
    _isInitialized = false;
  }

  /// Dispose isolate resources
  void _disposeIsolate() {
    if (_isolateInitialized && _syncSendPort != null) {
      // Send shutdown message to isolate
      _syncSendPort!.send({'type': 'shutdown', 'data': {}});
    }

    _syncIsolate?.kill(priority: Isolate.immediate);
    _syncReceivePort?.close();
    _syncIsolate = null;
    _syncSendPort = null;
    _syncReceivePort = null;
    _isolateInitialized = false;
    log('Isolate disposed');
  }

  /// Reset singleton instance
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }

  /// Get current sync status
  bool get isSyncing => _isSyncing;
  bool get isInitialized => _isInitialized;

  /// Manual refresh for testing (public method)
  Future<void> refreshHiveBoxes() async {
    await _refreshMainThreadHiveBoxes();
  }
}

/// Perform sync operation in isolate
Future<void> _performIsolateSyncOperation(SendPort mainSendPort) async {
  try {
    mainSendPort
        .send({'type': 'log', 'data': 'Starting sync operation in isolate...'});

    mainSendPort.send({'type': 'status', 'data': SyncStatus.syncing.index});

    // Check internet connectivity
    bool isOnline = await Utility.isConnected();
    if (!isOnline) {
      mainSendPort.send({
        'type': 'log',
        'data': 'No internet connection confirmed in isolate, skipping sync'
      });
      mainSendPort.send({'type': 'status', 'data': SyncStatus.idle.index});
      return;
    }

    // Perform sync operations
    await Future.wait([
      _syncSurveyDataInIsolate(mainSendPort),
      _syncImageDataInIsolate(mainSendPort),
    ]);

    mainSendPort
        .send({'type': 'sync_complete', 'data': 'Sync completed successfully'});
  } catch (e) {
    mainSendPort.send({'type': 'sync_error', 'data': e.toString()});
  }
}

/// Sync survey data in isolate
Future<void> _syncSurveyDataInIsolate(SendPort mainSendPort) async {
  try {
    mainSendPort.send(
        {'type': 'log', 'data': 'Starting survey data sync in isolate...'});

    // Close and reopen the box to ensure we get the latest data
    if (Hive.isBoxOpen('surveyBox')) {
      await Hive.box('surveyBox').close();
    }
    final box = await Hive.openBox('surveyBox');
    final totalRecords = box.keys.length;

    mainSendPort.send({
      'type': 'log',
      'data': 'Isolate: surveyBox reopened, found $totalRecords records'
    });

    if (totalRecords == 0) {
      mainSendPort.send({'type': 'log', 'data': 'No survey records to sync'});
      return;
    }

    mainSendPort.send(
        {'type': 'log', 'data': 'Found $totalRecords survey records to sync'});

    int syncedCount = 0;
    int errorCount = 0;

    // Process surveys in small batches
    const batchSize = 20;
    final keys = box.keys.toList();

    for (int i = 0; i < keys.length; i += batchSize) {
      final batchKeys = keys.skip(i).take(batchSize);

      for (final key in batchKeys) {
        try {
          final surveyData = box.get(key);
          if (surveyData != null && surveyData['isUploaded'] != true) {
            mainSendPort.send({
              'type': 'log',
              'data': 'Asset Type in isolate sync: ${surveyData['assetType']}'
            });

            final response =
                await _syncSurveyInIsolate(surveyData, mainSendPort);
            if (response) {
              surveyData['isUploaded'] = true;
              await box.put(key, surveyData);
              log('uploaded status : ${surveyData['isUploaded']}');
              syncedCount++;
              mainSendPort.send(
                  {'type': 'log', 'data': 'Survey $key uploaded successfully'});
            } else {
              errorCount++;
              mainSendPort.send(
                  {'type': 'log', 'data': 'Failed to upload survey $key'});
            }
          }
        } catch (e) {
          errorCount++;
          mainSendPort
              .send({'type': 'log', 'data': 'Error syncing survey $key: $e'});
        }
      }

      // Progress update
      mainSendPort.send({
        'type': 'progress',
        'data': {
          'current': i + batchSize,
          'total': keys.length,
          'operation': 'Syncing surveys',
        }
      });

      // Small delay between batches
      await Future.delayed(const Duration(milliseconds: 50));
    }

    mainSendPort.send({
      'type': 'log',
      'data':
          'Survey sync completed: $syncedCount/$totalRecords synced, $errorCount errors'
    });
  } catch (e) {
    mainSendPort.send({'type': 'log', 'data': 'Survey sync failed: $e'});
  }
}

/// Sync individual survey in isolate
Future<bool> _syncSurveyInIsolate(dynamic data, SendPort mainSendPort) async {
  final SurveyService surveyService = SurveyService();

  var locationCheck = data['location'];
  var manualEnteredLocation = data['manualEnteredLocation'];
  String? filteredLandMark;
  if (locationCheck == null) {
    String landMark =
        await _getLocationInIsolate(data['latitude'], data['longitude']);
    String? concatenatedLocation = '$manualEnteredLocation,$landMark';
    filteredLandMark = concatenatedLocation
        .replaceAll(RegExp(r',\s*'), ',')
        .replaceAll(RegExp(r'^,\s*'), '')
        .trim();
  }

  final isPole = data['assetType'] == 'Pole';
  final isSwitchPoint = data['assetType'] == 'Switch Point';
  final isTransformer = data['assetType'] == 'Transformer';
  final installedOn = int.tryParse(data['installedOn'] ?? '0') ?? 0;
  final armCount = int.tryParse(data['armCount'] ?? '0') ?? 0;

  final Map<String, dynamic> postData = {
    "roadType": data['roadType'],
    "trafficDensity": data['trafficDensity'],
    "trafficSpeed": data['trafficSpeed'],
    "assetType": data['assetType'],
    "customerId": data['customerId'],
    "wardId": data['wardId'],
    if (data['comments'] != '') "remarks": data['comments'],
    "region": data['region'],
    "zoneName": data['zone'],
    "wardName": data['ward'],
    "installedBy": data['installedBy'],
    "installedOn": installedOn,
    "state": 'INSTALLED',
    "latitude": data['latitude'],
    "longitude": data['longitude'],
    "location": filteredLandMark,
    "accuracy": double.parse(data['accuracy'] == '' ? '0' : data['accuracy']),
    "altitude": data['altitude'],
    "existingRoadCategory": data['roadCategory'],
    "roadWidth": data['roadWidth'],
    "vehicleAccess": data['vehicleAccess'],
    "signalStrength": {
      data["carrierName"]: data['signalStrengthLevel'],
    },
    "auditImg": [
      if (data['uuidFileName1'] != '') data['uuidFileName1'],
      if (data['uuidFileName2'] != '') data['uuidFileName2'],
      if (data['uuidFileName3'] != '') data['uuidFileName3'],
    ]
  };

  if (isPole) {
    postData.addAll({
      "name": data['poleNumber'],
      "condition": data['poleCondition'],
    });

    if (data['switchPointNo'] != '') {
      postData["switchPointNo"] = data['switchPointNo'];
    }
    if (data['escomPoleNumber'] != '') {
      postData["ebPoleNo"] = data['escomPoleNumber'];
    }
    if (data['exCorpPoleNo'] != '') {
      postData["exCorpPoleNo"] = data['exCorpPoleNo'];
    }
    if (data['poleTransformerNo'] != '') {
      postData["transformerNo"] = data['poleTransformerNo'];
    }
    if (data['poleCondition'] != 'Missing') {
      postData.addAll({
        "type": data['poleType'],
        "height": data['poleHeight'],
        "earthingRequired": data['earthingRequired'],
        "manualSwitchControl": data['manualSwitchControl'],
        "incomingTransmissionLine": data['incomingTransLine'],
        "incomingTransmissionType": data['incomingTransType'],
        "armCount": armCount,
        "armDetails": {
          "count": armCount,
          "length": data['armLength'],
          "condition": [
            {"condition": "Good", "count": data['goodArmCount']},
            {"condition": "Bad", "count": data['badArmCount']},
            {"condition": "Missing", "count": data['missingArmCount']},
          ]
        },
      });
      if (armCount > 0) {
        if (data['selectedLamps'].length > 0) {
          postData['lampProfiles'] = data['selectedLamps'];
        }
        postData['lightDetails'] = [
          {
            "condition": "Working",
            "count": data['workingCount'],
          },
          {
            "condition": "Not Working",
            "count": data['notWorkingCount'],
          },
          {
            "condition": "Missing",
            "count": data['missingCount'],
          },
        ];
      }

      if (data['poleType'] == 'High Mast(HM)') {
        postData['motorCondition'] = data['motorCondition'];
        if (data['motorCondition'] != 'Missing') {
          postData["motorDetails"] = {
            'rating': data['motorRating'],
            'make': data['motorMake'],
            'model': data['motorModel']
          };
          postData['winchCondition'] = data['winchCondition'];
          postData['ropeCondition'] = data['ropeCondition'];
        }
      }
      if (data['poleType'] != 'High Mast(HM)' &&
          data['poleType'] != 'Mini Mast(MM)') {
        postData["span"] = data['poleSpan'];
        postData["bracketMountingHeight"] = data['bracketMountingHeight'];
        if (data['clampType'] != 'Clamp Type Not Required') {
          postData["clampDimension"] = {
            "length": data['clampTypeLength'],
            "width": data['clampTypeWidth'],
            "unit": data['clampTypeUnits'],
          };
        }
      }
    }
  }

  if (isSwitchPoint) {
    postData.addAll({
      "switchPointNumber": data['spNo'],
      "switchPointType": data['spType'],
      "connectedLoad": (data['spconnectedLoad']?.isNotEmpty ?? false)
          ? '${data['spconnectedLoad']} kW'
          : '0 kW',
      "condition": data['spCondition'],
      "earthingCondition": data['spEarthingCondition'],
    });

    if (data['rrNo'] != '') postData["rrNumber"] = data['rrNo'];
    if (data['spId'] != '') postData["panelId"] = data['spId'];
    if (data['spTransformerNo'] != '') {
      postData["transformerNo"] = data['spTransformerNo'];
    }

    if (data['spMeter'] == 'Working') {
      postData["meterDetails"] = {"status": data['spMeter']};
    } else {
      postData["meterDetails"] = {
        "no": data['spMeterNo'],
        "type": data['spMeterType'],
        "make": data['spMeterMake'],
        "phase": data['spMeterPhase'],
        "status": data['spMeter'],
      };
    }
  }

  if (isTransformer) {
    postData.addAll({
      "transformerNumber": data['transformerNo'],
      "capacity": (data['transCapacity'].isNotEmpty ?? false)
          ? '${data['transCapacity']} kW'
          : '0 kW',
    });
  }

  var result = await surveyService.updateSurveyDetailService(postData);
  log('Survey details $postData uploaded successfully in isolate');
  if (result == "200") {
    mainSendPort.send({
      'type': 'log',
      'data': 'Survey details uploaded successfully in isolate'
    });
    return true;
  } else if (result == "400") {
    return true;
  }
  return false;
}

/// Get location in isolate
Future<String> _getLocationInIsolate(lat, long) async {
  List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
  Placemark place = placemarks[0];
  var locationData =
      '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
  return locationData;
}

/// Sync image data in isolate
Future<void> _syncImageDataInIsolate(SendPort mainSendPort) async {
  try {
    mainSendPort
        .send({'type': 'log', 'data': 'Starting image sync in isolate...'});

    // Close and reopen the box to ensure we get the latest data
    if (Hive.isBoxOpen('imagesBox')) {
      await Hive.box('imagesBox').close();
    }
    final imagesBox = await Hive.openBox('imagesBox');
    mainSendPort.send({
      'type': 'log',
      'data': 'Isolate: imagesBox reopened, found ${imagesBox.length} records'
    });

    if (imagesBox.length == 0) {
      mainSendPort.send({'type': 'log', 'data': 'No images to sync'});
      return;
    }

    int syncedCount = 0;
    int errorCount = 0;

    for (int i = 0; i < imagesBox.length; i++) {
      try {
        final imageMap = imagesBox.getAt(i) as Map<dynamic, dynamic>;
        final model = MultiCapturedImageModel.fromMap(imageMap);

        // Skip if all images are already uploaded
        if (model.isAllUploaded) {
          mainSendPort.send({
            'type': 'log',
            'data': 'All images for record $i already uploaded, skipping'
          });
          continue;
        }

        bool updated = false;

        // Upload image 1
        if (!model.isUploaded1) {
          if (model.fileName1.isNotEmpty && model.filePath1.isNotEmpty) {
            mainSendPort.send({
              'type': 'log',
              'data': 'Uploading 1st image in isolate: ${model.fileName1}'
            });
            final res1 = await _updateSurveyImageFromFileInIsolate(
                model.filePath1, model.fileName1, mainSendPort);
            if (res1 == true) {
              model.isUploaded1 = true;
              updated = true;
              syncedCount++;
              mainSendPort.send({
                'type': 'log',
                'data': 'Image 1 uploaded in isolate: ${model.fileName1}'
              });
              // Delete the compressed file after successful upload
              await ImageCompressionUtil.deleteCompressedImage(model.filePath1);
            } else {
              errorCount++;
            }
          } else {
            // Mark as uploaded if no image was captured
            model.isUploaded1 = true;
            updated = true;
            mainSendPort.send({
              'type': 'log',
              'data': 'Image 1 not captured - marking as uploaded'
            });
          }
        }

        // Upload image 2
        if (!model.isUploaded2) {
          if (model.fileName2.isNotEmpty && model.filePath2.isNotEmpty) {
            mainSendPort.send({
              'type': 'log',
              'data': 'Uploading 2nd image in isolate: ${model.fileName2}'
            });
            final res2 = await _updateSurveyImageFromFileInIsolate(
                model.filePath2, model.fileName2, mainSendPort);
            if (res2 == true) {
              model.isUploaded2 = true;
              updated = true;
              syncedCount++;
              mainSendPort.send({
                'type': 'log',
                'data': 'Image 2 uploaded in isolate: ${model.fileName2}'
              });
              // Delete the compressed file after successful upload
              await ImageCompressionUtil.deleteCompressedImage(model.filePath2);
            } else {
              errorCount++;
            }
          } else {
            // Mark as uploaded if no image was captured
            model.isUploaded2 = true;
            updated = true;
            mainSendPort.send({
              'type': 'log',
              'data': 'Image 2 not captured - marking as uploaded'
            });
          }
        }

        // Upload image 3
        if (!model.isUploaded3) {
          if (model.fileName3.isNotEmpty && model.filePath3.isNotEmpty) {
            mainSendPort.send({
              'type': 'log',
              'data': 'Uploading 3rd image in isolate: ${model.fileName3}'
            });
            final res3 = await _updateSurveyImageFromFileInIsolate(
                model.filePath3, model.fileName3, mainSendPort);
            if (res3 == true) {
              model.isUploaded3 = true;
              updated = true;
              syncedCount++;
              mainSendPort.send({
                'type': 'log',
                'data': 'Image 3 uploaded in isolate: ${model.fileName3}'
              });
              // Delete the compressed file after successful upload
              await ImageCompressionUtil.deleteCompressedImage(model.filePath3);
            } else {
              errorCount++;
            }
          } else {
            // Mark as uploaded if no image was captured
            model.isUploaded3 = true;
            updated = true;
            mainSendPort.send({
              'type': 'log',
              'data': 'Image 3 not captured - marking as uploaded'
            });
          }
        }

        // Update the record if any changes were made
        if (updated) {
          await imagesBox.putAt(i, model.toMap());
          mainSendPort.send({
            'type': 'log',
            'data': 'Updated image record $i with new upload status'
          });
        }
      } catch (e) {
        errorCount++;
        mainSendPort.send({
          'type': 'log',
          'data': 'Error processing image record $i in isolate: $e'
        });
      }
    }

    mainSendPort.send({
      'type': 'log',
      'data':
          'Image sync completed in isolate: $syncedCount images uploaded, $errorCount errors'
    });
  } catch (e) {
    mainSendPort
        .send({'type': 'log', 'data': 'Image sync failed in isolate: $e'});
  }
}

/// Update survey image from file path in isolate
Future<bool> _updateSurveyImageFromFileInIsolate(
    String filePath, String filename, SendPort mainSendPort) async {
  try {
    final s3Service = S3UploadService();
    final result = await s3Service.updateSurveyImageFileS3(filePath, filename);

    if (result == "200") {
      return true;
    } else if (result == "400") {
      return false;
    }
    return false;
  } catch (e) {
    mainSendPort.send({
      'type': 'log',
      'data': 'Error uploading image file $filename in isolate: $e'
    });
    return false;
  }
}

/// Sync progress model
class SyncProgress {
  final int current;
  final int total;
  final String operation;
  final double percentage;

  SyncProgress({
    required this.current,
    required this.total,
    required this.operation,
  }) : percentage = total > 0 ? (current / total * 100) : 0;

  factory SyncProgress.fromMap(Map<String, dynamic> map) {
    return SyncProgress(
      current: map['current'] ?? 0,
      total: map['total'] ?? 0,
      operation: map['operation'] ?? '',
    );
  }
}

/// Sync status enum
enum SyncStatus {
  idle,
  syncing,
  completed,
  error,
}
